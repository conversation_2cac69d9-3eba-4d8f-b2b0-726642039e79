class Options::ContributorOptions < ApplicationService
  include ContributorOptionsHelper
  include PostgresHelper

  attr_accessor :params, :scoped_workspace

  def initialize(scoped_company, params, scoped_company_user = nil, workspace = nil, with_guest = false, help_desk_module = false)
    @scoped_company = scoped_company
    @params = params
    @scoped_company_user = scoped_company_user
    @scoped_workspace = workspace
    @with_guest = with_guest
    @is_help_desk_module = help_desk_module
  end

  def call
    if params[:weighted]
      weighted_options
    elsif params[:only_groups]
      only_group_options
    elsif params[:only_staff]
      only_staff_options
    elsif params[:only_agents]
      only_agent_options
    elsif params[:only_helpdesk_agents]
      only_helpdesk_agent_options
    elsif params[:only_members]
      only_member_options
    elsif params[:assigned_to_field] && params[:selected_menu] == 'people'
      all_people_options
    elsif params[:assigned_to_field] && params[:selected_menu] == 'groups'
      only_group_options
    elsif params[:is_access_groups]
      access_groups_and_members
    else
      base_options
    end
  end

  private

  def weighted_options
    current_contributor = []
    current_agents = []
    others = []
    company_contributors.each do |contributor|
      if contributor.name
        contributor_id = @scoped_company_user&.contributor_id
        if contributor_id == contributor.id
          current_contributor << contributor_option(contributor)
        elsif company_agents.include?(contributor)
          current_agents << contributor_option(contributor)
        else
          others << contributor_option(contributor)
        end
      end
    end

    current_agents + current_contributor + others
  end

  def only_group_options
    company_contributors.filter_map do |contributor|
      contributor_option(contributor) if contributor.name && contributor.contributor_type == "Group"
    end
  end

  def only_staff_options
    company_contributors.filter_map do |contributor|
      contributor_option(contributor) if contributor.name && contributor.contributor_type == "CompanyUser"
    end
  end

  def only_agent_options
    company_contributors.filter_map do |contributor|
      contributor_option(contributor) if contributor.name && company_agents.include?(contributor)
    end
  end

  def only_helpdesk_agent_options(type = nil)
    company_contributors.filter_map do |contributor|
      contributor_option(contributor, type) if contributor.name && helpdesk_agents.include?(contributor)
    end
  end

  def only_member_options
    company_contributors.filter_map do |contributor|
      contributor_option(contributor) if contributor.name && contributor.contributor_type != "Group"
    end
  end

  def access_groups_and_members
    result = []
    result.concat(only_member_options)
    result.concat(access_groups)
    result
  end

  def access_groups
    @scoped_company.msp_templates_groups.filter_map do |group|
      {
        id: group.id,
        name: group.name,
        type: 'AccessGroup',
        avatar_thumb_url: nil,
      }
    end
  end

  def base_options
    helpdesk_subscription = has_helpdesk_subscription?
    contributors = company_contributors.includes(:group, :company_user)
    my_options = contributors.filter_map do |contributor|
      if helpdesk_subscription || contributor.contributor_group&.workspace_id.nil?
        contributor_option(contributor) if contributor.name
      end
    end
    name_order(my_options)
  end

  def has_helpdesk_subscription?
    return true if @scoped_company_user&.super_admin? ||
                   @scoped_company.is_sample_company? ||
                   @scoped_company.has_current_free_trial? && @scoped_company.subscriptions.count == 0

    subscriptions = @scoped_company.subscriptions.where.not(status: "insolvent")
    subscriptions.exists?(module_type: ["full_platform", "it_help_desk"])
  end

  def contributor_option(contributor, type = nil)
    debugger
    company_user = contributor.company_user
    avatar_url = company_user_avatar(company_user.id) if contributor.contributor_type == "CompanyUser" && company_user
    active_status = true
    active_status = company_user.active_status if contributor.contributor_type == "CompanyUser" && company_user
    option = {
      id: contributor.id,
      name: contributor.name,
      email: contributor.email,
      root_id: contributor.root_id,
      type: contributor.contributor_type,
      avatar_thumb_url: avatar_url,
      active: active_status,
    }
    option[:linkable_type] = type if type.present?
    option
  end

  def company_agents
    @company_agents ||= begin
      admins = @scoped_company.admins
      contributor_ids = admins.contributor.contributor_ids_only_users
      @scoped_company.contributors
        .includes(:group, company_user: :user)
        .where(id: contributor_ids)
        .order("groups.name, users.last_name, users.first_name")
    end
  end

  def helpdesk_agents
    @helpdesk_agents ||= begin
      agents = @is_help_desk_module ? @scoped_workspace.workspace_agents : @scoped_company.helpdesk_agents
      contributor_ids = agents.contributor.contributor_ids_only_users
      @scoped_company.contributors
        .includes(:group, company_user: :user)
        .where(id: contributor_ids)
        .order("groups.name, users.last_name, users.first_name")
    end
  end

  def name_order(options)
    return options unless sort_preferences.present? && sort_preferences.include?("Last Name, First Name")

    company_users = CompanyUser.includes(:user).where(contributor_id: options.pluck(:id)).group_by(&:contributor_id)
    options.each do |option|
      next unless option[:type] == "CompanyUser"

      company_user = company_users[option[:id]].first
      option[:name] = "#{company_user.last_name} #{company_user.first_name}"
    end
    options
  end

  def all_people_options
    result = []
    my_options = company_contributors.filter_map do |contributor|
      contributor_option(contributor, 'Everyone') if contributor.name && contributor.contributor_type != "Group"
    end
    result += name_order(my_options)
    result += only_helpdesk_agent_options(@scoped_workspace.workspace_agents&.name)
    result += groups_with_write_permission
    result
  end

  def groups_with_write_permission
    groups = @scoped_company.groups.where("name != ? AND workspace_id IS NULL", "Everyone")
                                   .includes(contributor: [:privileges, :guest, company_user: :user])
                                   .select { |group| group_has_required_privilege?(group) }
    group_options(groups)
  end

  def group_options(groups)
    groups.flat_map do |group|
      contributor = group.contributor  
      contributor_ids = contributor.contributor_ids_only_users
      company_contributors.filter_map do |c|
        contributor_option(c, group.name) if c.name && c.contributor_type != "Group" && contributor_ids.include?(c.id)
      end
    end.compact
  end

  def group_has_required_privilege?(group)
    group.contributor.privileges.find { |d| d.workspace_id == @scoped_workspace.id && ["write", "readscoped", "scoped"].include?(d.permission_type) }
  end

  def sort_preferences
    @sort_preferences ||= begin
      sort = CustomFormField.find_by(id: params[:field_id])&.sort_list
      sort.present? && sort.is_a?(String) ? JSON.parse(sort) : sort
    end
  end

  def company_contributors
    @company_contributors ||= begin
      if @with_guest
        my_contributors = root_contributors_with_guests(@scoped_company).includes(:guest)
      else
        my_contributors = root_contributors(@scoped_company)
      end

      selected_group_contributors = params[:group_contributor_ids].presence && Contributor.includes(:group, company_user: :user).where(id: params[:group_contributor_ids])
      selected_group_members_cont_ids = selected_group_contributors&.map { |con| con.contributor_ids_only_users }&.flatten
      my_contributors = my_contributors.where(id: selected_group_members_cont_ids) if selected_group_members_cont_ids.present?
      my_contributors = my_contributors.where.not(id: filtered_agent_ids) if @is_help_desk_module

      contributor_params = {
        contributors: my_contributors,
        archived: params[:archived],
        limit: params['limit'],
        offset: params['offset'],
        includes: params['includes'],
        values: params['values'],
        excludes: params['excludes'],
        sort_preferences: sort_preferences,
        query: params['query'],
      }
      ContributorOptionsQuery.new(contributor_params).call
    end
  end

  def company_users_avatars
    @company_users_avatars ||= begin
      company_user_ids = company_contributors.select { |c| c.contributor_type == "CompanyUser" && c.company_user }
                                            .map { |c| c.company_user.id }
                                            .compact
      cfvs = CustomFormValue.joins(:custom_form_field)
                            .where(module_id: company_user_ids,
                                   custom_form_fields: { field_attribute_type: "avatar" },
                                   module_type: "CompanyUser")
                            .order(:order_position)
                            .group_by(&:module_id).values.map(&:first)
    end
  end

  def company_user_avatar(user_id)
    cfv = company_users_avatars.find { |c| c.module_id == user_id }&.as_json
    cfv[:attachment][:url] if cfv && cfv[:attachment]
  end

  def filtered_agent_ids
    @scoped_company.groups.where.not(workspace_id: [nil, @scoped_workspace.id]).pluck(:contributor_id)
  end
end
