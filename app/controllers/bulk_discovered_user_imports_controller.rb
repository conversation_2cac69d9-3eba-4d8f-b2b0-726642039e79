class BulkDiscoveredUserImportsController < AuthenticatedController
  def create
    users = params[:discovered_users]
    select_all = params[:select_all]
    users += handle_non_selected_users if select_all && company_discovered_users&.size > users.count

    if users.present?
      if import_users(users)
        render json: {}, status: :ok
        Pusher.trigger("#{scoped_company.id}users",'discovered-users', {})
      else
        render json: {invalid_emails: @invalid_emails}, status: :bad_request
      end
    else
      render json: {}, status: :bad_request
      Pusher.trigger("#{scoped_company.id}users",'discovered-users', {})
    end
  end

  private
  # If the 'select_all' parameter is set to true,
  # We need to update the users that have not been selected in the list.
  def handle_non_selected_users
    bulk_list_update = params[:update_all_fields]
    selected_users_ids = params[:discovered_users].pluck(:id)
    users = company_discovered_users.where.not(id: selected_users_ids).map do |user|
      user.as_json.tap do |u|
        bulk_list_update.keys.each { |field| u[field] = bulk_list_update[field] }
        u['custom_form_id'] = default_custom_form&.id
      end
    end
  end

  # Build lookup for discovered users params by email
  def formated_discovered_users(discovered_users)
    @formated_discovered_users ||= discovered_users.index_by { |du| du['email'] }
  end

  def formated_discovered_users_by_id(discovered_users)
    @formated_discovered_users_by_id ||= discovered_users.index_by { |du| du['id'] }
  end

  # Build users params
  def build_user_params user
    {
      first_name: user[:first_name] ||= "",
      last_name: user[:last_name] ||= "",
      email: user[:email]&.downcase
    }
  end

  # Assign attributes to user
  def set_user_attributes
    @user.assign_attributes(
      password: SecureRandom.hex(32),
      reset_password_token: SecureRandom.hex(32),
      confirmation_token: SecureRandom.hex(32),
      guid: SecureRandom.uuid
    )
    @user.generate_authentication_token!
  end

  # Import discovered users
  def create_discovered_users(discovered_users, disc_users_email, saved_users_by_disc_users_email)
    user_instances = []
    disc_users_emails_difference = disc_users_email - saved_users_by_disc_users_email.pluck(:email)
    email_domains = EmailDomain.all.index_by(&:domain)

    disc_users_emails_difference.each do |email|
      fetch_user_params = formated_discovered_users(discovered_users).fetch(email)
      user_params = build_user_params(fetch_user_params)
      @user = User.new(user_params)
      @user.email_domain_cache = email_domains
      set_user_attributes
      if @user.valid?
        user_instances << @user
      else
        @invalid_emails << @user.email
        next
      end
    end

    User.import user_instances
  end

  # Import discovered users & create company users
  def import_users(discovered_users)
    @invalid_emails = []
    imported_discovered_users_emails = []

    disc_users_email = discovered_users.map { |du| du['email'].downcase }
    disc_users =  scoped_company.discovered_users.where(id: discovered_users.pluck(:id))
    saved_users_by_disc_users_email = User.where(email: disc_users_email)
    create_discovered_users(discovered_users, disc_users_email, saved_users_by_disc_users_email)
    users_by_email = saved_users_by_disc_users_email.index_by(&:email)
    imported_users = []
    preloaded_data = {
      integration_users: company.integrations_users.index_by(&:email),
      helpdesk_cf_ids: company.custom_forms.where(company_module: 'helpdesk').pluck(:id),
      ticket_emails: get_ticket_emails(disc_users),
      company_guests: company.guests.index_by(&:email),
      contributors: Contributor.includes(:group, :company_user).where(company: scoped_company).index_by(&:id)
    }

    disc_users.each do |disc_user|
      next if @invalid_emails.include?(disc_user['email'].squish)
      discoverd_user_email = disc_user['email']
      @discovered_user_params = formated_discovered_users_by_id(discovered_users).fetch(disc_user['id'])
      updated_discovered_user_email = @discovered_user_params['email']
      @user = users_by_email[updated_discovered_user_email]

      @company_user = CompanyModule::CustomForms::EntityCreate.new(
        company_module_data: user_data,
        company: company,
        new_user: @user,
        current_user: current_company_user,
        form_fields: default_custom_form_fields,
        custom_form: default_custom_form,
        preloaded_data: preloaded_data,
        is_bulk_action: true
      ).call.custom_entity

      if @company_user.valid?
        imported_users << @company_user
        imported_discovered_users_emails << discoverd_user_email
      else
        @invalid_emails << @company_user.email if @company_user.errors.messages[:"user.Email"].present?
      end
    end
    CompanyModule::CustomForms::EntityCreate.new(
      current_user: current_company_user
    ).create_bulk_user_activity(imported_users)

    create_field_mappings(imported_users)

    scoped_company.discovered_users.where(email: imported_discovered_users_emails).update_all(status: 'imported', is_viewed: true) if imported_discovered_users_emails.present?
    @invalid_emails.empty?
  end
  
  def create_field_mappings(company_users)
    return if company_users.empty?

    field_mappings = company_users.map do |cu|
      {
        mapping: params['field_mapping'],
        entity_type: 'CompanyUser',
        entity_id: cu.id
      }
    end
    FieldMapping.insert_all(field_mappings)
  end

  def get_ticket_emails(disc_users)
    disc_users_emails = disc_users.map(&:email)
    modified_emails = disc_users_emails.map { |email| "%<#{email}>%" }

    company.ticket_emails.tickets_not_created.where(
      'ticket_emails.from LIKE ANY (ARRAY[?]) OR ticket_emails.from = ANY (ARRAY[?])',
      modified_emails, disc_users_emails
    ).load
  end

  def company
    @company ||= begin
      form_company = scoped_company
      unless current_user.super_admin?
        company_ids = all_expanded_privileges.where(permission_type: %w[write scoped]).pluck(:company_id).uniq
        raise Exceptions::AccessDenied unless company_ids.include?(form_company&.id)
      end
      form_company
    end
  end

  def company_discovered_users
    @company_discovered_users ||= scoped_company.discovered_users.where(status: params[:status])
  end

  def user_data
    CompanyModule::CustomForms::EntityDataForDiscoveredUser.new(self, @discovered_user_params)
  end

  def default_custom_form
    @default_custom_form ||= scoped_company.custom_forms.includes(:custom_form_fields)
                                                        .find_by(company_module: 'company_user', default: true)
  end

  def default_custom_form_fields
    @default_custom_form_fields ||= default_custom_form&.custom_form_fields
  end
end
