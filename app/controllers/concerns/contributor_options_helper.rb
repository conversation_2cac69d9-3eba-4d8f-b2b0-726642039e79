module ContributorO<PERSON>s<PERSON><PERSON>per

  def root_contributors(company)
    company.contributors.joins("""
      LEFT JOIN company_users
        ON company_users.contributor_id = contributors.id
          AND company_users.is_sample_company_user = false
          #{archived_user_clause}
      LEFT JOIN users
        ON users.id = company_users.user_id
          AND NOT users.super_admin
      LEFT JOIN groups
        ON groups.contributor_id = contributors.id
    """
    ).where("(company_users.id IS NOT NULL OR groups.id IS NOT NULL)")
  end

  def root_contributors_with_guests(company)
    company.contributors.joins("""
      LEFT JOIN company_users
        ON company_users.contributor_id = contributors.id
          AND company_users.is_sample_company_user = false
          #{archived_user_clause}
      LEFT JOIN users
        ON users.id = company_users.user_id
          AND NOT users.super_admin
      LEFT JOIN groups
        ON groups.contributor_id = contributors.id
      LEFT JOIN guests
        ON guests.contributor_id = contributors.id
          AND guests.workspace_id = #{scoped_workspace.id}
    """
    ).where("(company_users.id IS NOT NULL OR groups.id IS NOT NULL OR guests.id IS NOT NULL)")
  end

  def archived_user_clause
    params && params[:archived] == "all" ? "" : "AND company_users.archived_at IS NULL"
  end

  def member_emails(contributor_ids)
    user_emails = []
    user_contributors_ids = []
    contributors = Contributor.where(id: contributor_ids)

    contributors.each do |con|
      user_contributors_ids << con.contributor_ids_only_users
    end
    user_contributors_ids = user_contributors_ids.flatten

    sql = """
      SELECT
        users.email
      FROM
        users
      INNER JOIN company_users
        ON users.id = company_users.user_id
      WHERE company_users.contributor_id IN (?)
      AND company_users.out_of_office = false
    """
    query = ActiveRecord::Base.send(:sanitize_sql, [sql, user_contributors_ids])

    results = ActiveRecord::Base.connection.execute(query).to_a
    user_emails << results.map{ |r| r['email'] }
    user_emails.flatten
  end

  def contributors_for_connectors(search_text)
    company_contributors = root_contributors(current_company)
                            .includes(:group, company_user: :user)
                            .where(
                            """
                              lower(CONCAT(users.first_name, ' ',users.last_name)) LIKE ?
                              OR lower(groups.name) LIKE ?
                            """,
                            "%#{search_text}%",
                            "%#{search_text}%")
                            .order('groups.name ASC, users.first_name ASC, users.last_name ASC')
                            .limit(25)
    contributors_emails = company_contributors.map { |con| con.email }
    helpdesk_custom_emails = HelpdeskCustomEmail.where(email: contributors_emails).pluck(:email)

    { company_contributors: company_contributors, helpdesk_custom_emails: helpdesk_custom_emails }
  end
end
