# frozen_string_literal: true

class Api::V1::Options::OptionsController < Api::V1::AuthenticatedController
  include HandleCompanyCacheKeys
  include ValidateCacheKeyRequirements
  def index
    render json: options
  end

  def limit
    params[:limit] || 10
  end

  def offset
    params[:offset]
  end

  def includes
    parse_json_param(params[:includes])
  end

  def excludes
    parse_json_param(params[:excludes])
  end

  def query
    params[:query]
  end

  def parse_json_param(param)
    param.present? && param.is_a?(String) ? JSON.parse(param) : param
  end
end
