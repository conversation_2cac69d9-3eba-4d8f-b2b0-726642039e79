# frozen_string_literal: true

class Api::V1::Options::OptionsController < Api::V1::AuthenticatedController
  include HandleCompanyCacheKeys
  include ValidateCacheKeyRequirements
  def index
    debugger
    render json: options #it is calling the options method in the options model in contributer_options_controller.rb
  end

  def limit
    params[:limit] || 10
  end

  def offset
    params[:offset]
  end

  def includes
    parse_json_param(params[:includes])
  end

  def excludes
    parse_json_param(params[:excludes])
  end

  def query
    params[:query]
  end

  def parse_json_param(param)
    param.present? && param.is_a?(String) ? JSON.parse(param) : param
  end
end
